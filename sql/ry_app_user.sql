SET NAMES utf8mb4;

drop table if exists app_user;
create table app_user (
  user_id           bigint(20)      not null auto_increment    comment '用户ID',
  user_name         varchar(30)     not null                   comment '用户账号',
  password          varchar(100)    default ''                 comment '密码',
  coin_balance      bigint(20)      default 0                  comment '用户金币余额',
  vip_status        int(1)          default 0                  comment 'VIP状态（0非VIP 1VIP）',
  vip_expire_time   datetime                                   comment 'VIP到期时间',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (user_id)
) engine=innodb auto_increment=1 comment = 'APP用户信息表';
