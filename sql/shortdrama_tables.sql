-- 短剧业务数据库表结构
SET NAMES utf8mb4;

-- 1. 短剧分类表
DROP TABLE IF EXISTS `shortdrama_category`;
CREATE TABLE `shortdrama_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB COMMENT='短剧分类表';

-- 2. 短剧主表
DROP TABLE IF EXISTS `shortdrama`;
CREATE TABLE `shortdrama` (
  `drama_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '短剧ID',
  `drama_name` varchar(100) NOT NULL COMMENT '短剧名称',
  `drama_desc` text COMMENT '短剧描述',
  `cover_image` varchar(255) COMMENT '封面图片URL',
  `category_id` bigint(20) COMMENT '分类ID',
  `tags` varchar(500) COMMENT '标签，逗号分隔',
  `total_episodes` int(11) DEFAULT 0 COMMENT '总集数',
  `price_type` tinyint(1) DEFAULT 1 COMMENT '价格类型（1免费 2金币 3VIP）',
  `coin_price` int(11) DEFAULT 0 COMMENT '金币价格',
  `is_vip_free` tinyint(1) DEFAULT 0 COMMENT 'VIP是否免费（0否 1是）',
  `view_count` bigint(20) DEFAULT 0 COMMENT '观看次数',
  `like_count` bigint(20) DEFAULT 0 COMMENT '点赞数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1下架）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`drama_id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='短剧主表';

-- 3. 短剧剧集表
DROP TABLE IF EXISTS `shortdrama_episode`;
CREATE TABLE `shortdrama_episode` (
  `episode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '剧集ID',
  `drama_id` bigint(20) NOT NULL COMMENT '短剧ID',
  `episode_num` int(11) NOT NULL COMMENT '集数',
  `episode_name` varchar(100) COMMENT '集数标题',
  `video_url` varchar(255) NOT NULL COMMENT '视频URL',
  `duration` int(11) COMMENT '时长（秒）',
  `thumbnail` varchar(255) COMMENT '缩略图',
  `price_type` tinyint(1) DEFAULT 1 COMMENT '价格类型（1免费 2金币 3VIP）',
  `coin_price` int(11) DEFAULT 0 COMMENT '金币价格',
  `view_count` bigint(20) DEFAULT 0 COMMENT '观看次数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1下架）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`episode_id`),
  KEY `idx_drama` (`drama_id`),
  KEY `idx_episode_num` (`drama_id`, `episode_num`)
) ENGINE=InnoDB COMMENT='短剧剧集表';

-- 4. 用户观看历史表
DROP TABLE IF EXISTS `user_watch_history`;
CREATE TABLE `user_watch_history` (
  `history_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '历史ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `drama_id` bigint(20) NOT NULL COMMENT '短剧ID',
  `episode_id` bigint(20) NOT NULL COMMENT '剧集ID',
  `watch_progress` int(11) DEFAULT 0 COMMENT '观看进度（秒）',
  `is_finished` tinyint(1) DEFAULT 0 COMMENT '是否看完（0否 1是）',
  `last_watch_time` datetime COMMENT '最后观看时间',
  `create_time` datetime COMMENT '创建时间',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`history_id`),
  UNIQUE KEY `uk_user_episode` (`user_id`, `episode_id`),
  KEY `idx_user_drama` (`user_id`, `drama_id`),
  KEY `idx_last_watch` (`user_id`, `last_watch_time`)
) ENGINE=InnoDB COMMENT='用户观看历史表';

-- 5. 用户追剧表
DROP TABLE IF EXISTS `user_follow_drama`;
CREATE TABLE `user_follow_drama` (
  `follow_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '追剧ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `drama_id` bigint(20) NOT NULL COMMENT '短剧ID',
  `follow_time` datetime COMMENT '追剧时间',
  PRIMARY KEY (`follow_id`),
  UNIQUE KEY `uk_user_drama` (`user_id`, `drama_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_drama` (`drama_id`)
) ENGINE=InnoDB COMMENT='用户追剧表';

-- 6. 订单表
DROP TABLE IF EXISTS `shortdrama_order`;
CREATE TABLE `shortdrama_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_type` tinyint(1) NOT NULL COMMENT '订单类型（1金币充值 2剧集购买 3VIP订阅）',
  `drama_id` bigint(20) COMMENT '短剧ID（剧集购买时）',
  `episode_id` bigint(20) COMMENT '剧集ID（剧集购买时）',
  `coin_amount` int(11) COMMENT '金币数量',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` tinyint(1) COMMENT '支付方式（1微信 2支付宝）',
  `pay_status` tinyint(1) DEFAULT 0 COMMENT '支付状态（0待支付 1已支付 2已退款）',
  `trade_no` varchar(100) COMMENT '第三方交易号',
  `pay_time` datetime COMMENT '支付时间',
  `create_time` datetime COMMENT '创建时间',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user` (`user_id`),
  KEY `idx_pay_status` (`pay_status`)
) ENGINE=InnoDB COMMENT='订单表';

-- 7. 用户金币流水表
DROP TABLE IF EXISTS `user_coin_log`;
CREATE TABLE `user_coin_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '流水ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `change_type` tinyint(1) NOT NULL COMMENT '变化类型（1充值 2消费 3签到 4任务奖励）',
  `change_amount` int(11) NOT NULL COMMENT '变化金额（正数增加，负数减少）',
  `before_amount` int(11) NOT NULL COMMENT '变化前金额',
  `after_amount` int(11) NOT NULL COMMENT '变化后金额',
  `related_id` bigint(20) COMMENT '关联ID（订单ID、任务ID等）',
  `remark` varchar(255) COMMENT '备注',
  `create_time` datetime COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='用户金币流水表';

-- 8. 活动配置表
DROP TABLE IF EXISTS `activity_config`;
CREATE TABLE `activity_config` (
  `activity_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `activity_name` varchar(100) NOT NULL COMMENT '活动名称',
  `activity_type` tinyint(1) NOT NULL COMMENT '活动类型（1签到 2任务 3抽奖）',
  `activity_desc` text COMMENT '活动描述',
  `reward_type` tinyint(1) COMMENT '奖励类型（1金币 2VIP天数）',
  `reward_amount` int(11) COMMENT '奖励数量',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`activity_id`)
) ENGINE=InnoDB COMMENT='活动配置表';

-- 9. 用户活动参与记录表
DROP TABLE IF EXISTS `user_activity_log`;
CREATE TABLE `user_activity_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `participate_date` date NOT NULL COMMENT '参与日期',
  `reward_amount` int(11) COMMENT '获得奖励数量',
  `create_time` datetime COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  UNIQUE KEY `uk_user_activity_date` (`user_id`, `activity_id`, `participate_date`),
  KEY `idx_user` (`user_id`),
  KEY `idx_activity` (`activity_id`)
) ENGINE=InnoDB COMMENT='用户活动参与记录表'; 