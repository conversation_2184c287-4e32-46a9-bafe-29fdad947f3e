package com.ruoyi.user.api.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.annotation.Excel.ColumnType;
import com.ruoyi.common.core.annotation.Excel.Type;
import com.ruoyi.common.core.web.domain.BaseEntity;
import java.util.Date;

/**
 * APP用户对象 app_user
 * 
 * <AUTHOR>
 */
public class AppUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID", type = Type.EXPORT, cellType = ColumnType.NUMERIC, prompt = "用户ID")
    private Long userId;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 密码 */
    private String password;

    /** 用户金币余额 */
    @Excel(name = "金币余额", type = Type.EXPORT)
    private Long coinBalance;

    /** VIP状态（0非VIP 1VIP） */
    @Excel(name = "VIP状态", readConverterExp = "0=非VIP,1=VIP")
    private Integer vipStatus;

    /** VIP到期时间 */
    @Excel(name = "VIP到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    private Date vipExpireTime;

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public Long getCoinBalance()
    {
        return coinBalance;
    }

    public void setCoinBalance(Long coinBalance)
    {
        this.coinBalance = coinBalance;
    }

    public Integer getVipStatus()
    {
        return vipStatus;
    }

    public void setVipStatus(Integer vipStatus)
    {
        this.vipStatus = vipStatus;
    }

    public Date getVipExpireTime()
    {
        return vipExpireTime;
    }

    public void setVipExpireTime(Date vipExpireTime)
    {
        this.vipExpireTime = vipExpireTime;
    }
}
