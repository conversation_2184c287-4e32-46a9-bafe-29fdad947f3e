package com.ruoyi.user.api;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.user.api.domain.AppUser;

/**
 * APP用户远程服务
 *
 * <AUTHOR>
 */
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
    R<AppUser> getUserInfo(String username);

    /**
     * 注册用户信息
     *
     * @param appUser 用户信息
     * @return 结果
     */
    R<Boolean> registerUserInfo(AppUser appUser);
}
