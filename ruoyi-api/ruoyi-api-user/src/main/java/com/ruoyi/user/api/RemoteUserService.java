package com.ruoyi.user.api;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysUser;

/**
 * APP用户远程服务
 * 
 * <AUTHOR>
 */
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
    R<SysUser> getUserInfo(String username);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    R<Boolean> registerUserInfo(SysUser sysUser);
}
