package com.ruoyi.shortdrama.api.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 剧集信息VO
 * 
 * <AUTHOR>
 */
public class EpisodeInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 剧集ID */
    private Long episodeId;

    /** 剧集标题 */
    private String title;

    /** 剧集价格 */
    private BigDecimal price;

    /** 是否免费 */
    private Boolean isFree;

    /** 剧集时长 */
    private Integer duration;

    /** 短剧ID */
    private Long dramaId;

    /** 价格类型 1-免费 2-金币 3-VIP */
    private Integer priceType;

    /** 金币价格 */
    private Integer coinPrice;

    public Long getEpisodeId()
    {
        return episodeId;
    }

    public void setEpisodeId(Long episodeId)
    {
        this.episodeId = episodeId;
    }

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public Boolean getIsFree()
    {
        return isFree;
    }

    public void setIsFree(Boolean isFree)
    {
        this.isFree = isFree;
    }

    public Integer getDuration()
    {
        return duration;
    }

    public void setDuration(Integer duration)
    {
        this.duration = duration;
    }

    public Long getDramaId()
    {
        return dramaId;
    }

    public void setDramaId(Long dramaId)
    {
        this.dramaId = dramaId;
    }

    public Integer getPriceType()
    {
        return priceType;
    }

    public void setPriceType(Integer priceType)
    {
        this.priceType = priceType;
    }

    public Integer getCoinPrice()
    {
        return coinPrice;
    }

    public void setCoinPrice(Integer coinPrice)
    {
        this.coinPrice = coinPrice;
    }
}
