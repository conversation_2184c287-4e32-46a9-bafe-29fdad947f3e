package com.ruoyi.shortdrama.api;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.shortdrama.api.domain.vo.DramaInfoVo;
import com.ruoyi.shortdrama.api.domain.vo.EpisodeInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 短剧内容服务远程调用
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteContentService", value = "shortdrama-content")
public interface RemoteContentService
{
    /**
     * 获取短剧基础信息
     */
    @GetMapping("/drama/info/{dramaId}")
    R<DramaInfoVo> getDramaInfo(@PathVariable("dramaId") Long dramaId);

    /**
     * 获取剧集信息
     */
    @GetMapping("/episode/info/{episodeId}")  
    R<EpisodeInfoVo> getEpisodeInfo(@PathVariable("episodeId") Long episodeId);

    /**
     * 检查用户是否已购买剧集
     */
    @GetMapping("/episode/check/{userId}/{episodeId}")
    R<Boolean> checkUserPurchased(@PathVariable("userId") Long userId, 
                                  @PathVariable("episodeId") Long episodeId);

    /**
     * 增加观看次数
     */
    @GetMapping("/drama/view/{dramaId}")
    R<Void> incrementViewCount(@PathVariable("dramaId") Long dramaId);
} 