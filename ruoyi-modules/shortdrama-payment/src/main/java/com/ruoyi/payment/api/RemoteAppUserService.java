package com.ruoyi.payment.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.user.api.domain.AppUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * APP用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteAppUserService", value = ServiceNameConstants.SHORTDRAMA_USER_SERVICE)
public interface RemoteAppUserService
{
    /**
     * 通过用户ID查询用户信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    @GetMapping(value = "/user/{userId}")
    public R<AppUser> getUserInfo(@PathVariable("userId") Long userId);

    /**
     * 更新用户信息
     *
     * @param appUser 用户信息
     * @return 结果
     */
    @PutMapping(value = "/user")
    public R<Boolean> updateUserInfo(@RequestBody AppUser appUser);
}
