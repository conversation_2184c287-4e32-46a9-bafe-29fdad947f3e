package com.ruoyi.payment.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单对象 shortdrama_order
 * 
 * <AUTHOR>
 */
public class ShortdramaOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long orderId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 订单类型（1金币充值 2剧集购买 3VIP订阅） */
    @Excel(name = "订单类型", readConverterExp = "1=金币充值,2=剧集购买,3=VIP订阅")
    private Integer orderType;

    /** 短剧ID（剧集购买时） */
    private Long dramaId;

    /** 剧集ID（剧集购买时） */
    private Long episodeId;

    /** 金币数量 */
    @Excel(name = "金币数量")
    private Integer coinAmount;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /** 支付方式（1微信 2支付宝） */
    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝")
    private Integer payType;

    /** 支付状态（0待支付 1已支付 2已退款） */
    @Excel(name = "支付状态", readConverterExp = "0=待支付,1=已支付,2=已退款")
    private Integer payStatus;

    /** 第三方交易号 */
    @Excel(name = "第三方交易号")
    private String tradeNo;

    /** 支付时间 */
    @Excel(name = "支付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    // 关联字段
    private String userName;
    private String dramaName;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Long getDramaId() {
        return dramaId;
    }

    public void setDramaId(Long dramaId) {
        this.dramaId = dramaId;
    }

    public Long getEpisodeId() {
        return episodeId;
    }

    public void setEpisodeId(Long episodeId) {
        this.episodeId = episodeId;
    }

    public Integer getCoinAmount() {
        return coinAmount;
    }

    public void setCoinAmount(Integer coinAmount) {
        this.coinAmount = coinAmount;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDramaName() {
        return dramaName;
    }

    public void setDramaName(String dramaName) {
        this.dramaName = dramaName;
    }

    @Override
    public String toString() {
        return "ShortdramaOrder{" +
                "orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", userId=" + userId +
                ", orderType=" + orderType +
                ", dramaId=" + dramaId +
                ", episodeId=" + episodeId +
                ", coinAmount=" + coinAmount +
                ", payAmount=" + payAmount +
                ", payType=" + payType +
                ", payStatus=" + payStatus +
                ", tradeNo='" + tradeNo + '\'' +
                ", payTime=" + payTime +
                ", userName='" + userName + '\'' +
                ", dramaName='" + dramaName + '\'' +
                '}';
    }
} 