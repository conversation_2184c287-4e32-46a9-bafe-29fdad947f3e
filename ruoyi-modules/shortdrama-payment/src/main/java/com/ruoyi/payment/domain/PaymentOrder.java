package com.ruoyi.payment.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import java.math.BigDecimal;

/**
 * 支付订单对象 payment_order
 * 
 * <AUTHOR>
 */
public class PaymentOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long orderId;

    /** 用户ID */
    private Long userId;

    /** 订单金额 */
    private BigDecimal amount;

    /** 订单状态（0待支付 1已支付 2已取消） */
    private Integer status;

    /** 支付方式（0微信 1支付宝） */
    private Integer payMethod;

    // Getters and Setters
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }
}
