package com.ruoyi.payment.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.payment.mapper.PaymentOrderMapper;
import com.ruoyi.payment.service.IPaymentOrderService;
import com.ruoyi.payment.domain.PaymentOrder;


/**
 * 支付订单Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class PaymentOrderServiceImpl implements IPaymentOrderService
{
    @Autowired
    private PaymentOrderMapper paymentOrderMapper;



    /**
     * 查询支付订单
     *
     * @param orderId 支付订单主键
     * @return 支付订单
     */
    @Override
    public PaymentOrder selectPaymentOrderById(Long orderId)
    {
        return paymentOrderMapper.selectPaymentOrderById(orderId);
    }

    /**
     * 查询支付订单列表
     *
     * @param paymentOrder 支付订单
     * @return 支付订单
     */
    @Override
    public List<PaymentOrder> selectPaymentOrderList(PaymentOrder paymentOrder)
    {
        return paymentOrderMapper.selectPaymentOrderList(paymentOrder);
    }

    /**
     * 新增支付订单
     *
     * @param paymentOrder 支付订单
     * @return 结果
     */
    @Override
    public int insertPaymentOrder(PaymentOrder paymentOrder)
    {
        int rows = paymentOrderMapper.insertPaymentOrder(paymentOrder);
        // Example: Update user's coin balance after a successful payment
        // You would typically get the user ID from the paymentOrder or current security context
        // AppUser appUser = appUserService.selectAppUserById(paymentOrder.getUserId());
        // if (appUser != null) {
        //     appUser.setCoinBalance(appUser.getCoinBalance() + paymentOrder.getAmount().longValue()); // Example: add coins
        //     appUserService.updateAppUser(appUser);
        // }
        return rows;
    }

    /**
     * 修改支付订单
     *
     * @param paymentOrder 支付订单
     * @return 结果
     */
    @Override
    public int updatePaymentOrder(PaymentOrder paymentOrder)
    {
        return paymentOrderMapper.updatePaymentOrder(paymentOrder);
    }

    /**
     * 批量删除支付订单
     *
     * @param orderIds 需要删除的支付订单主键
     * @return 结果
     */
    @Override
    public int deletePaymentOrderByIds(Long[] orderIds)
    {
        return paymentOrderMapper.deletePaymentOrderByIds(orderIds);
    }

    /**
     * 删除支付订单信息
     *
     * @param orderId 支付订单主键
     * @return 结果
     */
    @Override
    public int deletePaymentOrderById(Long orderId)
    {
        return paymentOrderMapper.deletePaymentOrderById(orderId);
    }
}
