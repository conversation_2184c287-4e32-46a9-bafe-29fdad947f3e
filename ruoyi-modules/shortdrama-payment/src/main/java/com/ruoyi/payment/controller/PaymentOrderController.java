package com.ruoyi.payment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.payment.domain.PaymentOrder;
import com.ruoyi.payment.service.IPaymentOrderService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 支付订单Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payment/order")
public class PaymentOrderController extends BaseController
{
    @Autowired
    private IPaymentOrderService paymentOrderService;

    /**
     * 查询支付订单列表
     */
    @RequiresPermissions("payment:order:list")
    @GetMapping("/list")
    public TableDataInfo list(PaymentOrder paymentOrder)
    {
        startPage();
        List<PaymentOrder> list = paymentOrderService.selectPaymentOrderList(paymentOrder);
        return getDataTable(list);
    }

    /**
     * 获取支付订单详细信息
     */
    @RequiresPermissions("payment:order:query")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(paymentOrderService.selectPaymentOrderById(orderId));
    }

    /**
     * 新增支付订单
     */
    @RequiresPermissions("payment:order:add")
    @Log(title = "支付订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaymentOrder paymentOrder)
    {
        return toAjax(paymentOrderService.insertPaymentOrder(paymentOrder));
    }

    /**
     * 修改支付订单
     */
    @RequiresPermissions("payment:order:edit")
    @Log(title = "支付订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaymentOrder paymentOrder)
    {
        return toAjax(paymentOrderService.updatePaymentOrder(paymentOrder));
    }

    /**
     * 删除支付订单
     */
    @RequiresPermissions("payment:order:remove")
    @Log(title = "支付订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds)
    {
        return toAjax(paymentOrderService.deletePaymentOrderByIds(orderIds));
    }
}
