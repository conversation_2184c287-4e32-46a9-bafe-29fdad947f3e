package com.ruoyi.payment.service;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.payment.domain.ShortdramaOrder;

/**
 * 支付服务接口
 *
 * <AUTHOR>
 */
public interface IPaymentService
{
    /**
     * 创建剧集购买订单
     *
     * @param episodeId 剧集ID
     * @return 订单信息
     */
    R<ShortdramaOrder> createEpisodeOrder(Long episodeId);

    /**
     * 处理金币支付
     *
     * @param orderNo 订单号
     * @return 处理结果
     */
    R<String> processCoinPayment(String orderNo);
}
