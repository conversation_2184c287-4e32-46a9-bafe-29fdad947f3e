package com.ruoyi.payment.service.impl;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.payment.domain.ShortdramaOrder;
import com.ruoyi.payment.service.IPaymentService;
import com.ruoyi.shortdrama.api.RemoteContentService;
import com.ruoyi.shortdrama.api.domain.vo.EpisodeInfoVo;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.common.core.constant.SecurityConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * 支付服务实现
 * 
 * <AUTHOR>
 */
@Service
public class PaymentServiceImpl implements IPaymentService
{
    @Autowired
    private RemoteUserService remoteUserService;
    
    @Autowired
    private RemoteContentService remoteContentService;

    /**
     * 创建剧集购买订单
     */
    @Override
    @Transactional
    public R<ShortdramaOrder> createEpisodeOrder(Long episodeId) 
    {
        try {
            Long userId = SecurityUtils.getUserId();
            
            // 1. 检查用户是否已购买
            R<Boolean> checkResult = remoteContentService.checkUserPurchased(userId, episodeId);
            if (!R.isSuccess(checkResult) || checkResult.getData()) {
                return R.fail("您已购买过该剧集");
            }

            // 2. 获取剧集信息
            R<EpisodeInfoVo> episodeResult = remoteContentService.getEpisodeInfo(episodeId);
            if (!R.isSuccess(episodeResult)) {
                return R.fail("剧集信息获取失败");
            }
            
            EpisodeInfoVo episode = episodeResult.getData();
            if (episode.getPriceType() == 1) {
                return R.fail("该剧集为免费剧集，无需购买");
            }

            // 3. 检查用户VIP状态
            R<SysUser> userResult = remoteUserService.getUserInfo(userId, SecurityConstants.INNER);
            if (!R.isSuccess(userResult)) {
                return R.fail("用户信息获取失败");
            }
            
            SysUser user = userResult.getData();
            
            // VIP用户免费剧集处理
            if (episode.getPriceType() == 3 && user.getVipStatus() == 1) {
                // TODO: 直接标记为已购买，无需创建订单
                return R.fail("VIP用户免费观看，无需创建订单");
            }

            // 4. 检查金币余额
            if (episode.getPriceType() == 2) {
                if (user.getCoinBalance() < episode.getCoinPrice()) {
                    return R.fail("金币余额不足，请先充值");
                }
            }

            // 5. 创建订单
            ShortdramaOrder order = new ShortdramaOrder();
            order.setOrderNo(generateOrderNo());
            order.setUserId(userId);
            order.setOrderType(2); // 剧集购买
            order.setDramaId(episode.getDramaId());
            order.setEpisodeId(episodeId);
            order.setCoinAmount(episode.getCoinPrice());
            order.setPayAmount(BigDecimal.ZERO); // 金币支付
            order.setPayStatus(0); // 待支付

            // TODO: 保存订单到数据库

            return R.ok(order, "订单创建成功");
            
        } catch (Exception e) {
            return R.fail("订单创建失败：" + e.getMessage());
        }
    }

    /**
     * 处理金币支付
     */
    @Override
    @Transactional
    public R<String> processCoinPayment(String orderNo) 
    {
        try {
            // TODO: 1. 查询订单信息
            // TODO: 2. 扣除用户金币
            // TODO: 3. 更新订单状态
            // TODO: 4. 记录金币流水
            // TODO: 5. 标记剧集已购买
            
            return R.ok("支付成功");
            
        } catch (Exception e) {
            return R.fail("支付失败：" + e.getMessage());
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "SD" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }
} 