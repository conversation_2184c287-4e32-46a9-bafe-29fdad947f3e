package com.ruoyi.payment.mapper;

import com.ruoyi.payment.domain.PaymentOrder;
import java.util.List;

/**
 * 支付订单Mapper接口
 *
 * <AUTHOR>
 */
public interface PaymentOrderMapper
{
    /**
     * 查询支付订单
     *
     * @param orderId 支付订单主键
     * @return 支付订单
     */
    public PaymentOrder selectPaymentOrderById(Long orderId);

    /**
     * 查询支付订单列表
     *
     * @param paymentOrder 支付订单
     * @return 支付订单集合
     */
    public List<PaymentOrder> selectPaymentOrderList(PaymentOrder paymentOrder);

    /**
     * 新增支付订单
     *
     * @param paymentOrder 支付订单
     * @return 结果
     */
    public int insertPaymentOrder(PaymentOrder paymentOrder);

    /**
     * 修改支付订单
     *
     * @param paymentOrder 支付订单
     * @return 结果
     */
    public int updatePaymentOrder(PaymentOrder paymentOrder);

    /**
     * 删除支付订单
     *
     * @param orderId 支付订单主键
     * @return 结果
     */
    public int deletePaymentOrderById(Long orderId);

    /**
     * 批量删除支付订单
     *
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePaymentOrderByIds(Long[] orderIds);
}
