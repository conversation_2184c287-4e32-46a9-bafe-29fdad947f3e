package com.ruoyi.payment;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 商业化支付服务
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class ShortdramaPaymentApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(ShortdramaPaymentApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  商业化支付服务模块启动成功   ლ(´ڡ`ლ)ﾞ  \\n" +
                " .-------.       ____     __        \\n" +
                " |  _ _   \\      \\   \\   /  /    \\n" +
                " | ( ' )  |       \\  _. /  '       \\n" +
                " |(_ o _) /        _( )_ .'         \\n" +
                " | (_,_).' __  ___(_ o _)'          \\n" +
                " |  |\\\\ \\  |  ||   |(_,_)'         \\n" +
                " |  | \\\\ `'   /|   `-'  /           \\n" +
                " |  |  \\    /  \\      /           \\n" +
                " ''-'   `'-'    `-..-'              ");
    }
}
