package com.ruoyi.content.service;

import com.ruoyi.content.domain.Content;
import java.util.List;

/**
 * 短剧内容Service接口
 *
 * <AUTHOR>
 */
public interface IContentService
{
    /**
     * 查询短剧内容列表
     *
     * @param content 短剧内容
     * @return 短剧内容集合
     */
    public List<Content> selectContentList(Content content);

    /**
     * 新增短剧内容
     *
     * @param content 短剧内容
     * @return 结果
     */
    public int insertContent(Content content);

    /**
     * 修改短剧内容
     *
     * @param content 短剧内容
     * @return 结果
     */
    public int updateContent(Content content);

    /**
     * 删除短剧内容
     *
     * @param contentId 短剧内容ID
     * @return 结果
     */
    public int deleteContentById(Long contentId);

    /**
     * 批量删除短剧内容
     *
     * @param contentIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteContentByIds(Long[] contentIds);

    /**
     * 通过短剧内容ID查询短剧内容
     *
     * @param contentId 短剧内容ID
     * @return 结果
     */
    public Content selectContentById(Long contentId);
}
