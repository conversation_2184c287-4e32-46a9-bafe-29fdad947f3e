package com.ruoyi.content.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.content.domain.Content;
import com.ruoyi.content.service.IContentService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 短剧内容Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/content")
public class ContentController extends BaseController
{
    @Autowired
    private IContentService contentService;

    /**
     * 查询短剧内容列表
     */
    @RequiresPermissions("content:content:list")
    @GetMapping("/list")
    public TableDataInfo list(Content content)
    {
        startPage();
        List<Content> list = contentService.selectContentList(content);
        return getDataTable(list);
    }

    /**
     * 获取短剧内容详细信息
     */
    @RequiresPermissions("content:content:query")
    @GetMapping(value = "/{contentId}")
    public AjaxResult getInfo(@PathVariable("contentId") Long contentId)
    {
        return success(contentService.selectContentById(contentId));
    }

    /**
     * 新增短剧内容
     */
    @RequiresPermissions("content:content:add")
    @Log(title = "短剧内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Content content)
    {
        return toAjax(contentService.insertContent(content));
    }

    /**
     * 修改短剧内容
     */
    @RequiresPermissions("content:content:edit")
    @Log(title = "短剧内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Content content)
    {
        return toAjax(contentService.updateContent(content));
    }

    /**
     * 删除短剧内容
     */
    @RequiresPermissions("content:content:remove")
    @Log(title = "短剧内容", businessType = BusinessType.DELETE)
    @DeleteMapping("/{contentIds}")
    public AjaxResult remove(@PathVariable Long[] contentIds)
    {
        return toAjax(contentService.deleteContentByIds(contentIds));
    }
}
