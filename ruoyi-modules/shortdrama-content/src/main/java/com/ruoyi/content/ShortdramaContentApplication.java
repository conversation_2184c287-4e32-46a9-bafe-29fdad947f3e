package com.ruoyi.content;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 短剧内容服务
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class ShortdramaContentApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(ShortdramaContentApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  短剧内容服务模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \      \   \   /  /    \n" +
                " | ( ' )  |       \  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\ \  |  ||   |(_,_)'         \n" +
                " |  | \ `'   /|   `-'  /           \n" +
                " |  |  \    /  \      /           \n" +
                " ''-'   `'-'    `-..-'              ");
    }
}
