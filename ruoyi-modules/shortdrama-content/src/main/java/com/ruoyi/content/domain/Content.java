package com.ruoyi.content.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 短剧内容对象 content
 * 
 * <AUTHOR>
 */
public class Content extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 短剧ID */
    private Long contentId;

    /** 短剧名称 */
    private String contentName;

    /** 视频URL */
    private String videoUrl;

    /** 封面图片URL */
    private String coverImageUrl;

    /** 分类ID */
    private Long categoryId;

    /** 标签 */
    private String tags;

    // Getters and Setters
    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public String getContentName() {
        return contentName;
    }

    public void setContentName(String contentName) {
        this.contentName = contentName;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }
}
