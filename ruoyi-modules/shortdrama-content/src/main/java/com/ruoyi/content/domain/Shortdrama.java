package com.ruoyi.content.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 短剧对象 shortdrama
 * 
 * <AUTHOR>
 */
public class Shortdrama extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 短剧ID */
    private Long dramaId;

    /** 短剧名称 */
    @Excel(name = "短剧名称")
    private String dramaName;

    /** 短剧描述 */
    @Excel(name = "短剧描述")
    private String dramaDesc;

    /** 封面图片URL */
    @Excel(name = "封面图片")
    private String coverImage;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 标签 */
    @Excel(name = "标签")
    private String tags;

    /** 总集数 */
    @Excel(name = "总集数")
    private Integer totalEpisodes;

    /** 价格类型（1免费 2金币 3VIP） */
    @Excel(name = "价格类型", readConverterExp = "1=免费,2=金币,3=VIP")
    private Integer priceType;

    /** 金币价格 */
    @Excel(name = "金币价格")
    private Integer coinPrice;

    /** VIP是否免费（0否 1是） */
    @Excel(name = "VIP免费", readConverterExp = "0=否,1=是")
    private Integer isVipFree;

    /** 观看次数 */
    @Excel(name = "观看次数")
    private Long viewCount;

    /** 点赞数 */
    @Excel(name = "点赞数")
    private Long likeCount;

    /** 状态（0正常 1下架） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=下架")
    private String status;

    // 分类名称（关联查询字段）
    private String categoryName;

    public Long getDramaId() {
        return dramaId;
    }

    public void setDramaId(Long dramaId) {
        this.dramaId = dramaId;
    }

    public String getDramaName() {
        return dramaName;
    }

    public void setDramaName(String dramaName) {
        this.dramaName = dramaName;
    }

    public String getDramaDesc() {
        return dramaDesc;
    }

    public void setDramaDesc(String dramaDesc) {
        this.dramaDesc = dramaDesc;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Integer getTotalEpisodes() {
        return totalEpisodes;
    }

    public void setTotalEpisodes(Integer totalEpisodes) {
        this.totalEpisodes = totalEpisodes;
    }

    public Integer getPriceType() {
        return priceType;
    }

    public void setPriceType(Integer priceType) {
        this.priceType = priceType;
    }

    public Integer getCoinPrice() {
        return coinPrice;
    }

    public void setCoinPrice(Integer coinPrice) {
        this.coinPrice = coinPrice;
    }

    public Integer getIsVipFree() {
        return isVipFree;
    }

    public void setIsVipFree(Integer isVipFree) {
        this.isVipFree = isVipFree;
    }

    public Long getViewCount() {
        return viewCount;
    }

    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }

    public Long getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Long likeCount) {
        this.likeCount = likeCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    @Override
    public String toString() {
        return "Shortdrama{" +
                "dramaId=" + dramaId +
                ", dramaName='" + dramaName + '\'' +
                ", dramaDesc='" + dramaDesc + '\'' +
                ", coverImage='" + coverImage + '\'' +
                ", categoryId=" + categoryId +
                ", tags='" + tags + '\'' +
                ", totalEpisodes=" + totalEpisodes +
                ", priceType=" + priceType +
                ", coinPrice=" + coinPrice +
                ", isVipFree=" + isVipFree +
                ", viewCount=" + viewCount +
                ", likeCount=" + likeCount +
                ", status='" + status + '\'' +
                ", categoryName='" + categoryName + '\'' +
                '}';
    }
} 