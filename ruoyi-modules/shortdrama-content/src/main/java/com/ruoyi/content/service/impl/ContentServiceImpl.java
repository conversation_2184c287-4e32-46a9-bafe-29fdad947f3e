package com.ruoyi.content.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.content.mapper.ContentMapper;
import com.ruoyi.content.service.IContentService;
import com.ruoyi.content.domain.Content;

/**
 * 短剧内容Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ContentServiceImpl implements IContentService
{
    @Autowired
    private ContentMapper contentMapper;

    /**
     * 查询短剧内容
     *
     * @param contentId 短剧内容主键
     * @return 短剧内容
     */
    @Override
    public Content selectContentById(Long contentId)
    {
        return contentMapper.selectContentById(contentId);
    }

    /**
     * 查询短剧内容列表
     *
     * @param content 短剧内容
     * @return 短剧内容
     */
    @Override
    public List<Content> selectContentList(Content content)
    {
        return contentMapper.selectContentList(content);
    }

    /**
     * 新增短剧内容
     *
     * @param content 短剧内容
     * @return 结果
     */
    @Override
    public int insertContent(Content content)
    {
        return contentMapper.insertContent(content);
    }

    /**
     * 修改短剧内容
     *
     * @param content 短剧内容
     * @return 结果
     */
    @Override
    public int updateContent(Content content)
    {
        return contentMapper.updateContent(content);
    }

    /**
     * 批量删除短剧内容
     *
     * @param contentIds 需要删除的短剧内容主键
     * @return 结果
     */
    @Override
    public int deleteContentByIds(Long[] contentIds)
    {
        return contentMapper.deleteContentByIds(contentIds);
    }

    /**
     * 删除短剧内容信息
     *
     * @param contentId 短剧内容主键
     * @return 结果
     */
    @Override
    public int deleteContentById(Long contentId)
    {
        return contentMapper.deleteContentById(contentId);
    }
}
