<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.content.mapper.ContentMapper">

    <resultMap type="com.ruoyi.content.domain.Content" id="ContentResult">
        <result property="contentId"    column="content_id"    />
        <result property="contentName"    column="content_name"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="categoryId"    column="category_id"    />
        <result property="tags"    column="tags"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectContentVo">
        select content_id, content_name, video_url, cover_image_url, category_id, tags, create_time, update_time from content
    </sql>

    <select id="selectContentList" parameterType="com.ruoyi.content.domain.Content" resultMap="ContentResult">
        <include refid="selectContentVo"/>
        <where>
            <if test="contentName != null  and contentName != ''">
                and content_name like concat('%', #{contentName}, '%')
            </if>
            <if test="categoryId != null ">
                and category_id = #{categoryId}
            </if>
        </where>
    </select>

    <select id="selectContentById" parameterType="Long" resultMap="ContentResult">
        <include refid="selectContentVo"/>
        where content_id = #{contentId}
    </select>

    <insert id="insertContent" parameterType="com.ruoyi.content.domain.Content" useGeneratedKeys="true" keyProperty="contentId">
        insert into content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contentName != null">content_name,</if>
            <if test="videoUrl != null">video_url,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="tags != null">tags,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contentName != null">#{contentName},</if>
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="tags != null">#{tags},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateContent" parameterType="com.ruoyi.content.domain.Content">
        update content
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentName != null">content_name = #{contentName},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where content_id = #{contentId}
    </update>

    <delete id="deleteContentById" parameterType="Long">
        delete from content where content_id = #{contentId}
    </delete>

    <delete id="deleteContentByIds" parameterType="Long">
        delete from content where content_id in
        <foreach item="contentId" collection="array" open="(" separator="," close=")">
            #{contentId}
        </foreach>
    </delete>
</mapper>
