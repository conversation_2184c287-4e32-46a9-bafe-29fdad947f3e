<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recommend.mapper.RecommendationMapper">

    <resultMap type="com.ruoyi.recommend.domain.Recommendation" id="RecommendationResult">
        <result property="recommendId"    column="recommend_id"    />
        <result property="userId"    column="user_id"    />
        <result property="contentId"    column="content_id"    />
        <result property="recommendType"    column="recommend_type"    />
        <result property="score"    column="score"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRecommendationVo">
        select recommend_id, user_id, content_id, recommend_type, score, create_time, update_time from recommendation
    </sql>

    <select id="selectRecommendationList" parameterType="com.ruoyi.recommend.domain.Recommendation" resultMap="RecommendationResult">
        <include refid="selectRecommendationVo"/>
        <where>
            <if test="userId != null ">
                and user_id = #{userId}
            </if>
            <if test="contentId != null ">
                and content_id = #{contentId}
            </if>
            <if test="recommendType != null ">
                and recommend_type = #{recommendType}
            </if>
        </where>
    </select>

    <select id="selectRecommendationById" parameterType="Long" resultMap="RecommendationResult">
        <include refid="selectRecommendationVo"/>
        where recommend_id = #{recommendId}
    </select>

    <insert id="insertRecommendation" parameterType="com.ruoyi.recommend.domain.Recommendation" useGeneratedKeys="true" keyProperty="recommendId">
        insert into recommendation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="contentId != null">content_id,</if>
            <if test="recommendType != null">recommend_type,</if>
            <if test="score != null">score,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="contentId != null">#{contentId},</if>
            <if test="recommendType != null">#{recommendType},</if>
            <if test="score != null">#{score},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateRecommendation" parameterType="com.ruoyi.recommend.domain.Recommendation">
        update recommendation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="recommendType != null">recommend_type = #{recommendType},</if>
            <if test="score != null">score = #{score},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where recommend_id = #{recommendId}
    </update>

    <delete id="deleteRecommendationById" parameterType="Long">
        delete from recommendation where recommend_id = #{recommendId}
    </delete>

    <delete id="deleteRecommendationByIds" parameterType="Long">
        delete from recommendation where recommend_id in
        <foreach item="recommendId" collection="array" open="(" separator="," close=")">
            #{recommendId}
        </foreach>
    </delete>
</mapper>
