package com.ruoyi.recommend;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import com.ruoyi.common.security.annotation.EnableRyFeignClients;

/**
 * 推荐与互动服务
 *
 * <AUTHOR>
 */
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class ShortdramaRecommendApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(ShortdramaRecommendApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  推荐与互动服务模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \      \   \   /  /    \n" +
                " | ( ' )  |       \  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\ \  |  ||   |(_,_)'         \n" +
                " |  | \ `'   /|   `-'  /           \n" +
                " |  |  \    /  \      /           \n" +
                " ''-'   `'-'    `-..-'              ");
    }
}
