package com.ruoyi.recommend.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 短剧内容服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteContentService", value = ServiceNameConstants.SHORTDRAMA_CONTENT_SERVICE)
public interface RemoteContentService
{
    /**
     * 获取短剧内容信息
     *
     * @param contentId 短剧内容ID
     * @return 结果
     */
    @GetMapping(value = "/content/{contentId}")
    public R<String> getContentInfo(@PathVariable("contentId") Long contentId);
}
