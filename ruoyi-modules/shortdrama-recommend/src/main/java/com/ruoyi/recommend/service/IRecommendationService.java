package com.ruoyi.recommend.service;

import com.ruoyi.recommend.domain.Recommendation;
import java.util.List;

/**
 * 推荐内容Service接口
 *
 * <AUTHOR>
 */
public interface IRecommendationService
{
    /**
     * 查询推荐内容列表
     *
     * @param recommendation 推荐内容
     * @return 推荐内容集合
     */
    public List<Recommendation> selectRecommendationList(Recommendation recommendation);

    /**
     * 新增推荐内容
     *
     * @param recommendation 推荐内容
     * @return 结果
     */
    public int insertRecommendation(Recommendation recommendation);

    /**
     * 修改推荐内容
     *
     * @param recommendation 推荐内容
     * @return 结果
     */
    public int updateRecommendation(Recommendation recommendation);

    /**
     * 删除推荐内容
     *
     * @param recommendId 推荐内容ID
     * @return 结果
     */
    public int deleteRecommendationById(Long recommendId);

    /**
     * 批量删除推荐内容
     *
     * @param recommendIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteRecommendationByIds(Long[] recommendIds);

    /**
     * 通过推荐内容ID查询推荐内容
     *
     * @param recommendId 推荐内容ID
     * @return 结果
     */
    public Recommendation selectRecommendationById(Long recommendId);
}
