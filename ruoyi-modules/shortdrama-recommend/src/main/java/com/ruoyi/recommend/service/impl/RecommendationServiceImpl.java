package com.ruoyi.recommend.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.recommend.mapper.RecommendationMapper;
import com.ruoyi.recommend.service.IRecommendationService;
import com.ruoyi.recommend.domain.Recommendation;

/**
 * 推荐内容Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class RecommendationServiceImpl implements IRecommendationService
{
    @Autowired
    private RecommendationMapper recommendationMapper;

    /**
     * 查询推荐内容
     *
     * @param recommendId 推荐内容主键
     * @return 推荐内容
     */
    @Override
    public Recommendation selectRecommendationById(Long recommendId)
    {
        return recommendationMapper.selectRecommendationById(recommendId);
    }

    /**
     * 查询推荐内容列表
     *
     * @param recommendation 推荐内容
     * @return 推荐内容
     */
    @Override
    public List<Recommendation> selectRecommendationList(Recommendation recommendation)
    {
        return recommendationMapper.selectRecommendationList(recommendation);
    }

    /**
     * 新增推荐内容
     *
     * @param recommendation 推荐内容
     * @return 结果
     */
    @Override
    public int insertRecommendation(Recommendation recommendation)
    {
        return recommendationMapper.insertRecommendation(recommendation);
    }

    /**
     * 修改推荐内容
     *
     * @param recommendation 推荐内容
     * @return 结果
     */
    @Override
    public int updateRecommendation(Recommendation recommendation)
    {
        return recommendationMapper.updateRecommendation(recommendation);
    }

    /**
     * 批量删除推荐内容
     *
     * @param recommendIds 需要删除的推荐内容主键
     * @return 结果
     */
    @Override
    public int deleteRecommendationByIds(Long[] recommendIds)
    {
        return recommendationMapper.deleteRecommendationByIds(recommendIds);
    }

    /**
     * 删除推荐内容信息
     *
     * @param recommendId 推荐内容主键
     * @return 结果
     */
    @Override
    public int deleteRecommendationById(Long recommendId)
    {
        return recommendationMapper.deleteRecommendationById(recommendId);
    }
}
