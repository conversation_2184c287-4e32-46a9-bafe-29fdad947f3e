package com.ruoyi.recommend.mapper;

import com.ruoyi.recommend.domain.Recommendation;
import java.util.List;

/**
 * 推荐内容Mapper接口
 *
 * <AUTHOR>
 */
public interface RecommendationMapper
{
    /**
     * 查询推荐内容
     *
     * @param recommendId 推荐内容主键
     * @return 推荐内容
     */
    public Recommendation selectRecommendationById(Long recommendId);

    /**
     * 查询推荐内容列表
     *
     * @param recommendation 推荐内容
     * @return 推荐内容集合
     */
    public List<Recommendation> selectRecommendationList(Recommendation recommendation);

    /**
     * 新增推荐内容
     *
     * @param recommendation 推荐内容
     * @return 结果
     */
    public int insertRecommendation(Recommendation recommendation);

    /**
     * 修改推荐内容
     *
     * @param recommendation 推荐内容
     * @return 结果
     */
    public int updateRecommendation(Recommendation recommendation);

    /**
     * 删除推荐内容
     *
     * @param recommendId 推荐内容主键
     * @return 结果
     */
    public int deleteRecommendationById(Long recommendId);

    /**
     * 批量删除推荐内容
     *
     * @param recommendIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecommendationByIds(Long[] recommendIds);
}
