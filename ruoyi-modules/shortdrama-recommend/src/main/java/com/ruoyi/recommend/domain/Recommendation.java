package com.ruoyi.recommend.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 推荐内容对象 recommendation
 * 
 * <AUTHOR>
 */
public class Recommendation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 推荐ID */
    private Long recommendId;

    /** 用户ID */
    private Long userId;

    /** 短剧ID */
    private Long contentId;

    /** 推荐类型（0个性化推荐 1热门推荐 2排行榜） */
    private Integer recommendType;

    /** 推荐分数 */
    private Double score;

    // Getters and Setters
    public Long getRecommendId() {
        return recommendId;
    }

    public void setRecommendId(Long recommendId) {
        this.recommendId = recommendId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public Integer getRecommendType() {
        return recommendType;
    }

    public void setRecommendType(Integer recommendType) {
        this.recommendType = recommendType;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }
}
