package com.ruoyi.recommend.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.recommend.domain.Recommendation;
import com.ruoyi.recommend.service.IRecommendationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 推荐内容Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/recommend")
public class RecommendationController extends BaseController
{
    @Autowired
    private IRecommendationService recommendationService;

    /**
     * 查询推荐内容列表
     */
    @RequiresPermissions("recommend:recommend:list")
    @GetMapping("/list")
    public TableDataInfo list(Recommendation recommendation)
    {
        startPage();
        List<Recommendation> list = recommendationService.selectRecommendationList(recommendation);
        return getDataTable(list);
    }

    /**
     * 获取推荐内容详细信息
     */
    @RequiresPermissions("recommend:recommend:query")
    @GetMapping(value = "/{recommendId}")
    public AjaxResult getInfo(@PathVariable("recommendId") Long recommendId)
    {
        return success(recommendationService.selectRecommendationById(recommendId));
    }

    /**
     * 新增推荐内容
     */
    @RequiresPermissions("recommend:recommend:add")
    @Log(title = "推荐内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Recommendation recommendation)
    {
        return toAjax(recommendationService.insertRecommendation(recommendation));
    }

    /**
     * 修改推荐内容
     */
    @RequiresPermissions("recommend:recommend:edit")
    @Log(title = "推荐内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Recommendation recommendation)
    {
        return toAjax(recommendationService.updateRecommendation(recommendation));
    }

    /**
     * 删除推荐内容
     */
    @RequiresPermissions("recommend:recommend:remove")
    @Log(title = "推荐内容", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recommendIds}")
    public AjaxResult remove(@PathVariable Long[] recommendIds)
    {
        return toAjax(recommendationService.deleteRecommendationByIds(recommendIds));
    }
}
