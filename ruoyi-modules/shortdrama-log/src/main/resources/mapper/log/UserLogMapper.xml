<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.log.mapper.UserLogMapper">

    <resultMap type="com.ruoyi.log.domain.UserLog" id="UserLogResult">
        <result property="logId"    column="log_id"    />
        <result property="userId"    column="user_id"    />
        <result property="operationType"    column="operation_type"    />
        <result property="operationDetail"    column="operation_detail"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectUserLogVo">
        select log_id, user_id, operation_type, operation_detail, create_time from user_log
    </sql>

    <select id="selectUserLogList" parameterType="com.ruoyi.log.domain.UserLog" resultMap="UserLogResult">
        <include refid="selectUserLogVo"/>
        <where>
            <if test="userId != null ">
                and user_id = #{userId}
            </if>
            <if test="operationType != null  and operationType != ''">
                and operation_type like concat('%', #{operationType}, '%')
            </if>
        </where>
    </select>

    <select id="selectUserLogById" parameterType="Long" resultMap="UserLogResult">
        <include refid="selectUserLogVo"/>
        where log_id = #{logId}
    </select>

    <insert id="insertUserLog" parameterType="com.ruoyi.log.domain.UserLog" useGeneratedKeys="true" keyProperty="logId">
        insert into user_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="operationDetail != null">operation_detail,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="operationType != null">#{operationType},</if>
            <if test="operationDetail != null">#{operationDetail},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateUserLog" parameterType="com.ruoyi.log.domain.UserLog">
        update user_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="operationDetail != null">operation_detail = #{operationDetail},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteUserLogById" parameterType="Long">
        delete from user_log where log_id = #{logId}
    </delete>

    <delete id="deleteUserLogByIds" parameterType="Long">
        delete from user_log where log_id in
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>
