package com.ruoyi.log.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 用户日志对象 user_log
 * 
 * <AUTHOR>
 */
public class UserLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 用户ID */
    private Long userId;

    /** 操作类型 */
    private String operationType;

    /** 操作详情 */
    private String operationDetail;

    // Getters and Setters
    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDetail() {
        return operationDetail;
    }

    public void setOperationDetail(String operationDetail) {
        this.operationDetail = operationDetail;
    }
}
