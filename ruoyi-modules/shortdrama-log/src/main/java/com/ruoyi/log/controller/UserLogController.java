package com.ruoyi.log.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.log.domain.UserLog;
import com.ruoyi.log.service.IUserLogService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 用户日志Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log")
public class UserLogController extends BaseController
{
    @Autowired
    private IUserLogService userLogService;

    /**
     * 查询用户日志列表
     */
    @RequiresPermissions("log:log:list")
    @GetMapping("/list")
    public TableDataInfo list(UserLog userLog)
    {
        startPage();
        List<UserLog> list = userLogService.selectUserLogList(userLog);
        return getDataTable(list);
    }

    /**
     * 获取用户日志详细信息
     */
    @RequiresPermissions("log:log:query")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(userLogService.selectUserLogById(logId));
    }

    /**
     * 新增用户日志
     */
    @RequiresPermissions("log:log:add")
    @Log(title = "用户日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserLog userLog)
    {
        return toAjax(userLogService.insertUserLog(userLog));
    }

    /**
     * 修改用户日志
     */
    @RequiresPermissions("log:log:edit")
    @Log(title = "用户日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserLog userLog)
    {
        return toAjax(userLogService.updateUserLog(userLog));
    }

    /**
     * 删除用户日志
     */
    @RequiresPermissions("log:log:remove")
    @Log(title = "用户日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(userLogService.deleteUserLogByIds(logIds));
    }
}
