package com.ruoyi.log.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.R;

/**
 * 数据上报
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log")
public class LogController
{
    /**
     * 上报用户行为日志
     *
     * @param logData 日志数据
     * @return 结果
     */
    @PostMapping("/report")
    public R<Boolean> reportLog(@RequestBody String logData)
    {
        // TODO: Implement logic to process and store log data in ClickHouse/Elasticsearch
        System.out.println("Received log data: " + logData);
        return R.ok(true);
    }
}
