package com.ruoyi.log.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.log.mapper.UserLogMapper;
import com.ruoyi.log.service.IUserLogService;
import com.ruoyi.log.domain.UserLog;

/**
 * 用户日志Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class UserLogServiceImpl implements IUserLogService
{
    @Autowired
    private UserLogMapper userLogMapper;

    /**
     * 查询用户日志
     *
     * @param logId 用户日志主键
     * @return 用户日志
     */
    @Override
    public UserLog selectUserLogById(Long logId)
    {
        return userLogMapper.selectUserLogById(logId);
    }

    /**
     * 查询用户日志列表
     *
     * @param userLog 用户日志
     * @return 用户日志
     */
    @Override
    public List<UserLog> selectUserLogList(UserLog userLog)
    {
        return userLogMapper.selectUserLogList(userLog);
    }

    /**
     * 新增用户日志
     *
     * @param userLog 用户日志
     * @return 结果
     */
    @Override
    public int insertUserLog(UserLog userLog)
    {
        return userLogMapper.insertUserLog(userLog);
    }

    /**
     * 修改用户日志
     *
     * @param userLog 用户日志
     * @return 结果
     */
    @Override
    public int updateUserLog(UserLog userLog)
    {
        return userLogMapper.updateUserLog(userLog);
    }

    /**
     * 批量删除用户日志
     *
     * @param logIds 需要删除的用户日志主键
     * @return 结果
     */
    @Override
    public int deleteUserLogByIds(Long[] logIds)
    {
        return userLogMapper.deleteUserLogByIds(logIds);
    }

    /**
     * 删除用户日志信息
     *
     * @param logId 用户日志主键
     * @return 结果
     */
    @Override
    public int deleteUserLogById(Long logId)
    {
        return userLogMapper.deleteUserLogById(logId);
    }
}
