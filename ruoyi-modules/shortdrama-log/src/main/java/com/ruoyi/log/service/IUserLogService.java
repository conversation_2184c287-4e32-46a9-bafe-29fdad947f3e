package com.ruoyi.log.service;

import com.ruoyi.log.domain.UserLog;
import java.util.List;

/**
 * 用户日志Service接口
 *
 * <AUTHOR>
 */
public interface IUserLogService
{
    /**
     * 查询用户日志列表
     *
     * @param userLog 用户日志
     * @return 用户日志集合
     */
    public List<UserLog> selectUserLogList(UserLog userLog);

    /**
     * 新增用户日志
     *
     * @param userLog 用户日志
     * @return 结果
     */
    public int insertUserLog(UserLog userLog);

    /**
     * 修改用户日志
     *
     * @param userLog 用户日志
     * @return 结果
     */
    public int updateUserLog(UserLog userLog);

    /**
     * 删除用户日志
     *
     * @param logId 用户日志ID
     * @return 结果
     */
    public int deleteUserLogById(Long logId);

    /**
     * 批量删除用户日志
     *
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteUserLogByIds(Long[] logIds);

    /**
     * 通过用户日志ID查询用户日志
     *
     * @param logId 用户日志ID
     * @return 结果
     */
    public UserLog selectUserLogById(Long logId);
}
