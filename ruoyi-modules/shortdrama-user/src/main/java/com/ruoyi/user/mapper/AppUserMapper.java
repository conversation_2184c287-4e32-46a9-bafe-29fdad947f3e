package com.ruoyi.user.mapper;

import com.ruoyi.user.api.domain.AppUser;
import java.util.List;

/**
 * APP用户Mapper接口
 *
 * <AUTHOR>
 */
public interface AppUserMapper
{
    /**
     * 查询APP用户
     *
     * @param userId APP用户主键
     * @return APP用户
     */
    public AppUser selectAppUserById(Long userId);

    /**
     * 查询APP用户列表
     *
     * @param appUser APP用户
     * @return APP用户集合
     */
    public List<AppUser> selectAppUserList(AppUser appUser);

    /**
     * 新增APP用户
     *
     * @param appUser APP用户
     * @return 结果
     */
    public int insertAppUser(AppUser appUser);

    /**
     * 修改APP用户
     *
     * @param appUser APP用户
     * @return 结果
     */
    public int updateAppUser(AppUser appUser);

    /**
     * 删除APP用户
     *
     * @param userId APP用户主键
     * @return 结果
     */
    public int deleteAppUserById(Long userId);

    /**
     * 批量删除APP用户
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppUserByIds(Long[] userIds);

    /**
     * 通过用户名查询APP用户
     *
     * @param userName 用户名
     * @return APP用户对象信息
     */
    public AppUser selectAppUserByUserName(String userName);
}
