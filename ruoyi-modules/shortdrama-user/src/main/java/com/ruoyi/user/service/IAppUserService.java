package com.ruoyi.user.service;

import com.ruoyi.user.api.domain.AppUser;
import java.util.List;

/**
 * APP用户Service接口
 *
 * <AUTHOR>
 */
public interface IAppUserService
{
    /**
     * 查询APP用户列表
     *
     * @param appUser APP用户
     * @return APP用户集合
     */
    public List<AppUser> selectAppUserList(AppUser appUser);

    /**
     * 新增APP用户
     *
     * @param appUser APP用户
     * @return 结果
     */
    public int insertAppUser(AppUser appUser);

    /**
     * 修改APP用户
     *
     * @param appUser APP用户
     * @return 结果
     */
    public int updateAppUser(AppUser appUser);

    /**
     * 删除APP用户
     *
     * @param userId APP用户ID
     * @return 结果
     */
    public int deleteAppUserById(Long userId);

    /**
     * 批量删除APP用户
     *
     * @param userIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteAppUserByIds(Long[] userIds);

    /**
     * 通过用户ID查询APP用户
     *
     * @param userId APP用户ID
     * @return 结果
     */
    public AppUser selectAppUserById(Long userId);

    /**
     * 通过用户账号查询APP用户
     *
     * @param userName 用户账号
     * @return 结果
     */
    public AppUser selectAppUserByUserName(String userName);
}
