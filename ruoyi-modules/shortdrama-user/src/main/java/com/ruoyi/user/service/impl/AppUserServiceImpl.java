package com.ruoyi.user.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.user.mapper.AppUserMapper;
import com.ruoyi.user.service.IAppUserService;
import com.ruoyi.user.api.domain.AppUser;

/**
 * APP用户Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AppUserServiceImpl implements IAppUserService
{
    @Autowired
    private AppUserMapper appUserMapper;

    /**
     * 查询APP用户
     *
     * @param userId APP用户主键
     * @return APP用户
     */
    @Override
    public AppUser selectAppUserById(Long userId)
    {
        return appUserMapper.selectAppUserById(userId);
    }

    /**
     * 查询APP用户列表
     *
     * @param appUser APP用户
     * @return APP用户
     */
    @Override
    public List<AppUser> selectAppUserList(AppUser appUser)
    {
        return appUserMapper.selectAppUserList(appUser);
    }

    /**
     * 新增APP用户
     *
     * @param appUser APP用户
     * @return 结果
     */
    @Override
    public int insertAppUser(AppUser appUser)
    {
        return appUserMapper.insertAppUser(appUser);
    }

    /**
     * 修改APP用户
     *
     * @param appUser APP用户
     * @return 结果
     */
    @Override
    public int updateAppUser(AppUser appUser)
    {
        return appUserMapper.updateAppUser(appUser);
    }

    /**
     * 批量删除APP用户
     *
     * @param userIds 需要删除的APP用户主键
     * @return 结果
     */
    @Override
    public int deleteAppUserByIds(Long[] userIds)
    {
        return appUserMapper.deleteAppUserByIds(userIds);
    }

    /**
     * 删除APP用户信息
     *
     * @param userId APP用户主键
     * @return 结果
     */
    @Override
    public int deleteAppUserById(Long userId)
    {
        return appUserMapper.deleteAppUserById(userId);
    }

    /**
     * 通过用户名查询APP用户
     *
     * @param userName 用户名
     * @return APP用户对象信息
     */
    @Override
    public AppUser selectAppUserByUserName(String userName)
    {
        return appUserMapper.selectAppUserByUserName(userName);
    }
}
