<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.user.mapper.AppUserMapper">

    <resultMap type="com.ruoyi.user.api.domain.AppUser" id="AppUserResult">
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="password"    column="password"    />
        <result property="coinBalance"    column="coin_balance"    />
        <result property="vipStatus"    column="vip_status"    />
        <result property="vipExpireTime"    column="vip_expire_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppUserVo">
        select user_id, user_name, password, coin_balance, vip_status, vip_expire_time, create_time, update_time from app_user
    </sql>

    <select id="selectAppUserList" parameterType="com.ruoyi.user.api.domain.AppUser" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        <where>
            <if test="userName != null  and userName != ''">
                and user_name like concat('%', #{userName}, '%')
            </if>
        </where>
    </select>

    <select id="selectAppUserById" parameterType="Long" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectAppUserByUserName" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where user_name = #{userName}
    </select>

    <insert id="insertAppUser" parameterType="com.ruoyi.user.api.domain.AppUser" useGeneratedKeys="true" keyProperty="userId">
        insert into app_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">user_name,</if>
            <if test="password != null">password,</if>
            <if test="coinBalance != null">coin_balance,</if>
            <if test="vipStatus != null">vip_status,</if>
            <if test="vipExpireTime != null">vip_expire_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="coinBalance != null">#{coinBalance},</if>
            <if test="vipStatus != null">#{vipStatus},</if>
            <if test="vipExpireTime != null">#{vipExpireTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppUser" parameterType="com.ruoyi.user.api.domain.AppUser">
        update app_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="coinBalance != null">coin_balance = #{coinBalance},</if>
            <if test="vipStatus != null">vip_status = #{vipStatus},</if>
            <if test="vipExpireTime != null">vip_expire_time = #{vipExpireTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteAppUserById" parameterType="Long">
        delete from app_user where user_id = #{userId}
    </delete>

    <delete id="deleteAppUserByIds" parameterType="Long">
        delete from app_user where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
