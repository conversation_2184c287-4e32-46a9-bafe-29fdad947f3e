<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.activity.mapper.ActivityMapper">

    <resultMap type="com.ruoyi.activity.domain.Activity" id="ActivityResult">
        <result property="activityId"    column="activity_id"    />
        <result property="activityName"    column="activity_name"    />
        <result property="activityType"    column="activity_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectActivityVo">
        select activity_id, activity_name, activity_type, status, create_time, update_time from activity
    </sql>

    <select id="selectActivityList" parameterType="com.ruoyi.activity.domain.Activity" resultMap="ActivityResult">
        <include refid="selectActivityVo"/>
        <where>
            <if test="activityName != null  and activityName != ''">
                and activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="activityType != null ">
                and activity_type = #{activityType}
            </if>
            <if test="status != null ">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectActivityById" parameterType="Long" resultMap="ActivityResult">
        <include refid="selectActivityVo"/>
        where activity_id = #{activityId}
    </select>

    <insert id="insertActivity" parameterType="com.ruoyi.activity.domain.Activity" useGeneratedKeys="true" keyProperty="activityId">
        insert into activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityName != null">activity_name,</if>
            <if test="activityType != null">activity_type,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityName != null">#{activityName},</if>
            <if test="activityType != null">#{activityType},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateActivity" parameterType="com.ruoyi.activity.domain.Activity">
        update activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityName != null">activity_name = #{activityName},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where activity_id = #{activityId}
    </update>

    <delete id="deleteActivityById" parameterType="Long">
        delete from activity where activity_id = #{activityId}
    </delete>

    <delete id="deleteActivityByIds" parameterType="Long">
        delete from activity where activity_id in
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>
</mapper>
