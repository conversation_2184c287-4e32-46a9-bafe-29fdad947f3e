package com.ruoyi.activity.service;

import com.ruoyi.activity.domain.Activity;
import java.util.List;

/**
 * 活动Service接口
 *
 * <AUTHOR>
 */
public interface IActivityService
{
    /**
     * 查询活动列表
     *
     * @param activity 活动
     * @return 活动集合
     */
    public List<Activity> selectActivityList(Activity activity);

    /**
     * 新增活动
     *
     * @param activity 活动
     * @return 结果
     */
    public int insertActivity(Activity activity);

    /**
     * 修改活动
     *
     * @param activity 活动
     * @return 结果
     */
    public int updateActivity(Activity activity);

    /**
     * 删除活动
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public int deleteActivityById(Long activityId);

    /**
     * 批量删除活动
     *
     * @param activityIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteActivityByIds(Long[] activityIds);

    /**
     * 通过活动ID查询活动
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public Activity selectActivityById(Long activityId);
}
