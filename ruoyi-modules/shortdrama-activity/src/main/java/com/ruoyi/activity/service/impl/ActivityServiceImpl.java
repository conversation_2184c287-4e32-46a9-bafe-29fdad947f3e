package com.ruoyi.activity.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.activity.mapper.ActivityMapper;
import com.ruoyi.activity.service.IActivityService;
import com.ruoyi.activity.domain.Activity;


/**
 * 活动Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ActivityServiceImpl implements IActivityService
{
    @Autowired
    private ActivityMapper activityMapper;



    /**
     * 查询活动
     *
     * @param activityId 活动主键
     * @return 活动
     */
    @Override
    public Activity selectActivityById(Long activityId)
    {
        return activityMapper.selectActivityById(activityId);
    }

    /**
     * 查询活动列表
     *
     * @param activity 活动
     * @return 活动
     */
    @Override
    public List<Activity> selectActivityList(Activity activity)
    {
        return activityMapper.selectActivityList(activity);
    }

    /**
     * 新增活动
     *
     * @param activity 活动
     * @return 结果
     */
    @Override
    public int insertActivity(Activity activity)
    {
        int rows = activityMapper.insertActivity(activity);
        // Example: If activity involves claiming coins, update user's coin balance
        // You would typically get the user ID from the activity or current security context
        // AppUser appUser = appUserService.selectAppUserById(someUserId);
        // if (appUser != null) {
        //     appUser.setCoinBalance(appUser.getCoinBalance() + 100L); // Example: add 100 coins
        //     appUserService.updateAppUser(appUser);
        // }
        return rows;
    }

    /**
     * 修改活动
     *
     * @param activity 活动
     * @return 结果
     */
    @Override
    public int updateActivity(Activity activity)
    {
        return activityMapper.updateActivity(activity);
    }

    /**
     * 批量删除活动
     *
     * @param activityIds 需要删除的活动主键
     * @return 结果
     */
    @Override
    public int deleteActivityByIds(Long[] activityIds)
    {
        return activityMapper.deleteActivityByIds(activityIds);
    }

    /**
     * 删除活动信息
     *
     * @param activityId 活动主键
     * @return 结果
     */
    @Override
    public int deleteActivityById(Long activityId)
    {
        return activityMapper.deleteActivityById(activityId);
    }
}
