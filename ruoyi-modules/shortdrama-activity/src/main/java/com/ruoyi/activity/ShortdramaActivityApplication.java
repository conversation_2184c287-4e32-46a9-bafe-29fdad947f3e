package com.ruoyi.activity;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 活动与推送服务
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class ShortdramaActivityApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(ShortdramaActivityApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  活动与推送服务模块启动成功   ლ(´ڡ`ლ)ﾞ  
" +
                " .-------.       ____     __        
" +
                " |  _ _   \      \   \   /  /    
" +
                " | ( ' )  |       \  _. /  '       
" +
                " |(_ o _) /        _( )_ .'         
" +
                " | (_,_).' __  ___(_ o _)'          
" +
                " |  |\ \  |  ||   |(_,_)'         
" +
                " |  | \ `'   /|   `-'  /           
" +
                " |  |  \    /  \      /           
" +
                " ''-'   `'-'    `-..-'              ");
    }
}
