package com.ruoyi.activity.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 活动对象 activity
 * 
 * <AUTHOR>
 */
public class Activity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID */
    private Long activityId;

    /** 活动名称 */
    private String activityName;

    /** 活动类型（0签到 1任务 2领金币） */
    private Integer activityType;

    /** 活动状态（0未开始 1进行中 2已结束） */
    private Integer status;

    // Getters and Setters
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
