# gateway 网关配置
spring:
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: ruoyi-auth
          uri: lb://ruoyi-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 代码生成
        - id: ruoyi-gen
          uri: lb://ruoyi-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 定时任务
        - id: ruoyi-job
          uri: lb://ruoyi-job
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: ruoyi-system
          uri: lb://ruoyi-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: ruoyi-file
          uri: lb://ruoyi-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1
        # 短剧内容服务
        - id: shortdrama-content
          uri: lb://shortdrama-content
          predicates:
            - Path=/content/**
          filters:
            - StripPrefix=1
        # 短剧支付服务
        - id: shortdrama-payment
          uri: lb://shortdrama-payment
          predicates:
            - Path=/payment/**
          filters:
            - StripPrefix=1
        # 短剧推荐服务
        - id: shortdrama-recommend
          uri: lb://shortdrama-recommend
          predicates:
            - Path=/recommend/**
          filters:
            - StripPrefix=1
        # 短剧活动服务
        - id: shortdrama-activity
          uri: lb://shortdrama-activity
          predicates:
            - Path=/activity/**
          filters:
            - StripPrefix=1
        # 短剧日志服务
        - id: shortdrama-log
          uri: lb://shortdrama-log
          predicates:
            - Path=/log/**
          filters:
            - StripPrefix=1

# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
  # 不校验白名单
  ignore:
    whites:
      - /auth/logout
      - /auth/login
      - /auth/register
      - /*/v2/api-docs
      - /csrf
      - /content/app/**
      - /recommend/app/**
      - /activity/app/**
      - /payment/app/** 