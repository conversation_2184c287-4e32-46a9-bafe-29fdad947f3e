spring:
  cloud:
    gateway:
      routes:
        - id: shortdrama-content
          uri: lb://shortdrama-content
          predicates:
            - Path=/content/**
        - id: shortdrama-payment
          uri: lb://shortdrama-payment
          predicates:
            - Path=/payment/**
        - id: shortdrama-recommend
          uri: lb://shortdrama-recommend
          predicates:
            - Path=/recommend/**
        - id: shortdrama-activity
          uri: lb://shortdrama-activity
          predicates:
            - Path=/activity/**
        - id: shortdrama-log
          uri: lb://shortdrama-log
          predicates:
            - Path=/log/**
        - id: shortdrama-user
          uri: lb://shortdrama-user
          predicates:
            - Path=/user/**
