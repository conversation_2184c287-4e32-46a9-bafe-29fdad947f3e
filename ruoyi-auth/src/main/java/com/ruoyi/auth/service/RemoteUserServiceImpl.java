package com.ruoyi.auth.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.service.ISysUserService;

/**
 * APP用户远程服务实现
 *
 * <AUTHOR>
 */
@Service
public class RemoteUserServiceImpl implements RemoteUserService
{
    @Autowired
    private ISysUserService sysUserService;

    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
    @Override
    public R<SysUser> getUserInfo(String username)
    {
        SysUser sysUser = sysUserService.selectUserByUserName(username);
        return R.ok(sysUser);
    }

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @Override
    public R<Boolean> registerUserInfo(SysUser sysUser)
    {
        // Implement registration logic here, e.g., check if username exists, encrypt password, insert user
        // For now, a placeholder implementation
        boolean result = sysUserService.insertUser(sysUser) > 0;
        return R.ok(result);
    }
}
