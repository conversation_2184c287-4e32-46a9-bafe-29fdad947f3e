package com.ruoyi.auth.service;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.user.api.domain.AppUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * APP用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteAppUserService", value = ServiceNameConstants.SHORTDRAMA_USER_SERVICE)
public interface RemoteAppUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
    @GetMapping(value = "/user/info/{username}")
    public R<AppUser> getUserInfo(@PathVariable("username") String username);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @PostMapping(value = "/user/register")
    public R<Boolean> registerUserInfo(@RequestBody AppUser appUser);
}
